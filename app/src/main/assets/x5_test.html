<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X5 WebView 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .info-card {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover {
            background: #45a049;
        }
        .status {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success { color: #4CAF50; }
        .warning { color: #FF9800; }
        .error { color: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 X5 WebView 测试页面</h1>
            <p>测试腾讯X5内核的功能和兼容性</p>
        </div>

        <div class="info-card">
            <h3>📱 设备信息</h3>
            <div id="device-info">
                <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
                <p><strong>屏幕分辨率:</strong> <span id="screen-resolution"></span></p>
                <p><strong>浏览器语言:</strong> <span id="browser-language"></span></p>
            </div>
        </div>

        <div class="info-card">
            <h3>🔧 WebView 信息</h3>
            <div id="webview-info">
                <p><strong>是否为X5内核:</strong> <span id="is-x5" class="status"></span></p>
                <p><strong>X5版本:</strong> <span id="x5-version" class="status"></span></p>
                <p><strong>Chrome版本:</strong> <span id="chrome-version" class="status"></span></p>
            </div>
        </div>

        <div class="info-card">
            <h3>🧪 功能测试</h3>
            <button class="button" onclick="testLocalStorage()">测试 LocalStorage</button>
            <button class="button" onclick="testSessionStorage()">测试 SessionStorage</button>
            <button class="button" onclick="testGeolocation()">测试 地理位置</button>
            <button class="button" onclick="testWebGL()">测试 WebGL</button>
            <button class="button" onclick="testCanvas()">测试 Canvas</button>
            <button class="button" onclick="testWebSocket()">测试 WebSocket</button>
            <div id="test-results" class="status"></div>
        </div>

        <div class="info-card">
            <h3>📞 原生接口测试</h3>
            <button class="button" onclick="testX5Interface()">测试 X5 接口</button>
            <button class="button" onclick="testAndroidInterface()">测试 Android 接口</button>
            <div id="interface-results" class="status"></div>
        </div>

        <div class="info-card">
            <h3>🌐 网络测试</h3>
            <button class="button" onclick="loadDotfashion()">加载 DotFashion</button>
            <button class="button" onclick="loadBaidu()">加载 百度</button>
            <button class="button" onclick="testAjax()">测试 AJAX</button>
            <div id="network-results" class="status"></div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePageInfo();
            detectWebViewType();
        });

        function initializePageInfo() {
            // 设备信息
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('screen-resolution').textContent = screen.width + 'x' + screen.height;
            document.getElementById('browser-language').textContent = navigator.language;

            // Chrome版本检测
            const chromeMatch = navigator.userAgent.match(/Chrome\/(\d+\.\d+\.\d+\.\d+)/);
            const chromeVersion = chromeMatch ? chromeMatch[1] : '未知';
            document.getElementById('chrome-version').textContent = chromeVersion;
        }

        function detectWebViewType() {
            // 检测是否为X5内核
            let isX5 = false;
            let x5Version = '未知';

            // 检查全局变量
            if (window.IS_X5_WEBVIEW) {
                isX5 = true;
                x5Version = window.X5_WEBVIEW_VERSION || '未知';
            }

            // 检查X5接口
            if (window.X5Android) {
                try {
                    isX5 = window.X5Android.isX5Available();
                    x5Version = window.X5Android.getX5Version();
                } catch (e) {
                    console.log('X5接口调用失败:', e);
                }
            }

            // 更新显示
            const isX5Element = document.getElementById('is-x5');
            const x5VersionElement = document.getElementById('x5-version');

            if (isX5) {
                isX5Element.textContent = '是 ✅';
                isX5Element.className = 'status success';
                x5VersionElement.textContent = x5Version;
                x5VersionElement.className = 'status success';
            } else {
                isX5Element.textContent = '否 (系统WebView) ❌';
                isX5Element.className = 'status warning';
                x5VersionElement.textContent = '不适用';
                x5VersionElement.className = 'status warning';
            }
        }

        function testLocalStorage() {
            try {
                localStorage.setItem('test', 'X5测试');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                updateTestResults('LocalStorage: ✅ 支持');
            } catch (e) {
                updateTestResults('LocalStorage: ❌ 不支持 - ' + e.message);
            }
        }

        function testSessionStorage() {
            try {
                sessionStorage.setItem('test', 'X5测试');
                const value = sessionStorage.getItem('test');
                sessionStorage.removeItem('test');
                updateTestResults('SessionStorage: ✅ 支持');
            } catch (e) {
                updateTestResults('SessionStorage: ❌ 不支持 - ' + e.message);
            }
        }

        function testGeolocation() {
            if (navigator.geolocation) {
                updateTestResults('Geolocation: ✅ API可用');
            } else {
                updateTestResults('Geolocation: ❌ API不可用');
            }
        }

        function testWebGL() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    updateTestResults('WebGL: ✅ 支持');
                } else {
                    updateTestResults('WebGL: ❌ 不支持');
                }
            } catch (e) {
                updateTestResults('WebGL: ❌ 错误 - ' + e.message);
            }
        }

        function testCanvas() {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    updateTestResults('Canvas: ✅ 支持');
                } else {
                    updateTestResults('Canvas: ❌ 不支持');
                }
            } catch (e) {
                updateTestResults('Canvas: ❌ 错误 - ' + e.message);
            }
        }

        function testWebSocket() {
            if (window.WebSocket) {
                updateTestResults('WebSocket: ✅ API可用');
            } else {
                updateTestResults('WebSocket: ❌ API不可用');
            }
        }

        function testX5Interface() {
            if (window.X5Android) {
                try {
                    window.X5Android.showToast('X5接口测试成功！');
                    window.X5Android.logMessage('X5接口测试日志');
                    updateInterfaceResults('X5接口: ✅ 可用');
                } catch (e) {
                    updateInterfaceResults('X5接口: ❌ 错误 - ' + e.message);
                }
            } else {
                updateInterfaceResults('X5接口: ❌ 不可用');
            }
        }

        function testAndroidInterface() {
            if (window.Android) {
                try {
                    updateInterfaceResults('Android接口: ✅ 可用');
                } catch (e) {
                    updateInterfaceResults('Android接口: ❌ 错误 - ' + e.message);
                }
            } else {
                updateInterfaceResults('Android接口: ❌ 不可用');
            }
        }

        function loadDotfashion() {
            updateNetworkResults('正在加载 DotFashion...');
            window.location.href = 'https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login';
        }

        function loadBaidu() {
            updateNetworkResults('正在加载 百度...');
            window.location.href = 'https://baidu.com';
        }

        function testAjax() {
            updateNetworkResults('测试 AJAX 请求...');
            fetch('https://httpbin.org/get')
                .then(response => response.json())
                .then(data => {
                    updateNetworkResults('AJAX: ✅ 成功');
                })
                .catch(error => {
                    updateNetworkResults('AJAX: ❌ 失败 - ' + error.message);
                });
        }

        function updateTestResults(message) {
            const element = document.getElementById('test-results');
            element.innerHTML += '<div>' + message + '</div>';
        }

        function updateInterfaceResults(message) {
            const element = document.getElementById('interface-results');
            element.innerHTML += '<div>' + message + '</div>';
        }

        function updateNetworkResults(message) {
            const element = document.getElementById('network-results');
            element.innerHTML += '<div>' + message + '</div>';
        }

        // 控制台输出
        console.log('X5 WebView 测试页面已加载');
        console.log('User Agent:', navigator.userAgent);
        console.log('是否为X5:', window.IS_X5_WEBVIEW || false);
        console.log('X5版本:', window.X5_WEBVIEW_VERSION || '未知');
    </script>
</body>
</html>
