<!DOCTYPE html>
<html>
<head>
    <style>
        .receipt {
            width: 300px;
            padding: 10px;
            margin: 20px auto;
            border: 1px solid #ccc;
        }
        .center { text-align: center; }
        .right { text-align: right; }
        .divider {
            border-top: 1px dashed #000;
            margin: 8px 0;
        }
        button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
        }

    </style>
    <title>本地调试功能</title>
</head>
<body>
<h1 style="text-align: center">本地调试功能</h1>
<button onclick="startScan()">扫码</button>
<button onclick="PrintText()">打印收据</button>
<button onclick="getSN()">获取SN号</button>
<button onclick="playPaySound()">播放支付音效</button>

<p id="result"></p>
<script type="text/javascript">
    function startScan() {
        Android.startScan();
    }

    function onScanResult(result) {
        document.getElementById('result').innerText = result;
    }

    function PrintText() {
        document.getElementById('result').innerText = '打印测试中';
        Android.print();
    }

    function getSN() {
        const sn = Android.getSN();
        document.getElementById('result').innerText = '设备SN号：' + sn;
    }

    function playPaySound() {
        document.getElementById('result').innerText = '播放支付成功音效';
        Android.playPaySound();
    }

    function previewTone() {
        Android.previewTone();
    }


</script>
</body>
</html>