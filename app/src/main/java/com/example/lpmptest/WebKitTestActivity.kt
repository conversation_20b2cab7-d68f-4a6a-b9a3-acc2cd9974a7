package com.example.lpmptest

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.webkit.WebViewCompat
import androidx.webkit.WebSettingsCompat
import androidx.webkit.WebViewFeature

/**
 * AndroidX WebKit功能测试Activity
 * 用于测试和展示AndroidX WebKit库的各种功能
 */
class WebKitTestActivity : AppCompatActivity() {
    
    private lateinit var webView: WebView
    private lateinit var statusTextView: TextView
    
    companion object {
        fun start(context: Context) {
            val intent = Intent(context, WebKitTestActivity::class.java)
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 创建简单的布局
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }
        
        // 状态显示文本
        statusTextView = TextView(this).apply {
            text = "正在初始化AndroidX WebKit..."
            textSize = 14f
            setPadding(0, 0, 0, 16)
        }
        layout.addView(statusTextView)
        
        // 测试按钮
        val testButton = Button(this).apply {
            text = "测试AndroidX WebKit功能"
            setOnClickListener { testWebKitFeatures() }
        }
        layout.addView(testButton)
        
        // WebView
        webView = WebView(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
            
            // 基本设置
            settings.apply {
                javaScriptEnabled = true
                domStorageEnabled = true
            }
            
            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    Log.d("WebKitTest", "页面加载完成: $url")
                }
            }
        }
        layout.addView(webView)
        
        setContentView(layout)
        
        // 初始化AndroidX WebKit功能
        initializeWebKit()
    }
    
    private fun initializeWebKit() {
        try {
            val features = mutableListOf<String>()
            
            // 获取WebView版本信息
            val webViewPackageInfo = WebViewCompat.getCurrentWebViewPackage(this)
            if (webViewPackageInfo != null) {
                features.add("WebView版本: ${webViewPackageInfo.versionName}")
                Log.d("WebKitTest", "WebView版本: ${webViewPackageInfo.versionName}")
            }
            
            // 检查关键功能
            val keyFeatures = listOf(
                WebViewFeature.ALGORITHMIC_DARKENING,
                WebViewFeature.SAFE_BROWSING_ENABLE,
                WebViewFeature.MULTI_PROCESS,
                WebViewFeature.WEB_MESSAGE_LISTENER
            )
            
            keyFeatures.forEach { feature ->
                val isSupported = WebViewFeature.isFeatureSupported(feature)
                features.add("${feature}: ${if (isSupported) "支持" else "不支持"}")
                Log.d("WebKitTest", "${feature}: ${if (isSupported) "支持" else "不支持"}")
            }
            
            // 启用支持的功能
            if (WebViewFeature.isFeatureSupported(WebViewFeature.ALGORITHMIC_DARKENING)) {
                WebSettingsCompat.setAlgorithmicDarkeningAllowed(webView.settings, true)
                features.add("已启用算法暗色模式")
            }
            
            if (WebViewFeature.isFeatureSupported(WebViewFeature.SAFE_BROWSING_ENABLE)) {
                WebSettingsCompat.setSafeBrowsingEnabled(webView.settings, true)
                features.add("已启用安全浏览")
            }
            
            // 更新状态显示
            statusTextView.text = features.joinToString("\n")
            
            // 加载测试页面
            webView.loadUrl("https://www.google.com")
            
        } catch (e: Exception) {
            Log.e("WebKitTest", "初始化AndroidX WebKit失败", e)
            statusTextView.text = "初始化失败: ${e.message}"
        }
    }
    
    private fun testWebKitFeatures() {
        try {
            val testResults = mutableListOf<String>()
            
            // 测试各种功能
            testResults.add("=== AndroidX WebKit功能测试 ===")
            
            // 检查多进程模式
            if (WebViewFeature.isFeatureSupported(WebViewFeature.MULTI_PROCESS)) {
                val isMultiProcess = WebViewCompat.isMultiProcessEnabled()
                testResults.add("多进程模式: ${if (isMultiProcess) "已启用" else "未启用"}")
            }
            
            // 检查WebView客户端
            val currentClient = WebViewCompat.getWebViewClient(webView)
            testResults.add("WebView客户端: ${currentClient?.javaClass?.simpleName ?: "默认"}")
            
            // 检查WebView渲染进程
            if (WebViewFeature.isFeatureSupported(WebViewFeature.GET_WEB_VIEW_RENDERER)) {
                val renderer = WebViewCompat.getWebViewRenderProcess(webView)
                testResults.add("渲染进程: ${if (renderer != null) "已获取" else "未获取"}")
            }
            
            // 显示测试结果
            android.app.AlertDialog.Builder(this)
                .setTitle("AndroidX WebKit测试结果")
                .setMessage(testResults.joinToString("\n"))
                .setPositiveButton("确定", null)
                .show()
                
        } catch (e: Exception) {
            Log.e("WebKitTest", "测试AndroidX WebKit功能失败", e)
            android.app.AlertDialog.Builder(this)
                .setTitle("测试失败")
                .setMessage("测试失败: ${e.message}")
                .setPositiveButton("确定", null)
                .show()
        }
    }
}
