package com.example.lpmptest

import android.app.Application
import android.util.Log
import com.tencent.smtt.export.external.TbsCoreSettings
import com.tencent.smtt.sdk.QbSdk
import com.tencent.smtt.sdk.TbsListener

/**
 * 自定义Application类，用于初始化X5内核
 */
class LpmpApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        
        Log.d("X5", "开始初始化X5内核...")
        
        // 初始化X5内核
        initX5WebView()
    }

    private fun initX5WebView() {
        try {
            // 设置X5内核配置
            val map = HashMap<String, Any>()
            map[TbsCoreSettings.TBS_SETTINGS_USE_SPEEDY_CLASSLOADER] = true
            map[TbsCoreSettings.TBS_SETTINGS_USE_DEXLOADER_SERVICE] = true
            QbSdk.initTbsSettings(map)

            // 设置X5内核监听器
            val tbsListener = object : TbsListener {
                override fun onDownloadFinish(i: Int) {
                    Log.d("X5", "X5内核下载完成，状态码: $i")
                    when (i) {
                        100 -> Log.d("X5", "下载成功")
                        else -> Log.w("X5", "下载状态: $i")
                    }
                }

                override fun onInstallFinish(i: Int) {
                    Log.d("X5", "X5内核安装完成，状态码: $i")
                    when (i) {
                        200 -> Log.d("X5", "安装成功")
                        else -> Log.w("X5", "安装状态: $i")
                    }
                }

                override fun onDownloadProgress(i: Int) {
                    Log.d("X5", "X5内核下载进度: $i%")
                }
            }

            // 设置监听器
            QbSdk.setTbsListener(tbsListener)

            // 初始化X5环境
            QbSdk.initX5Environment(applicationContext, object : QbSdk.PreInitCallback {
                override fun onViewInitFinished(isSuccess: Boolean) {
                    Log.d("X5", "X5内核初始化完成: $isSuccess")
                    if (isSuccess) {
                        Log.d("X5", "X5内核初始化成功，可以使用X5 WebView")
                    } else {
                        Log.w("X5", "X5内核初始化失败，将使用系统WebView")
                    }
                }

                override fun onCoreInitFinished() {
                    Log.d("X5", "X5内核核心初始化完成")
                }
            })

            Log.d("X5", "X5内核初始化请求已发送")

        } catch (e: Exception) {
            Log.e("X5", "初始化X5内核时发生错误", e)
        }
    }

    companion object {
        /**
         * 检查X5内核是否可用
         */
        fun isX5Available(): Boolean {
            return try {
                QbSdk.canLoadX5(null)
            } catch (e: Exception) {
                Log.e("X5", "检查X5可用性时发生错误", e)
                false
            }
        }

        /**
         * 获取X5内核版本
         */
        fun getX5Version(): Int {
            return try {
                QbSdk.getTbsVersion(null)
            } catch (e: Exception) {
                Log.e("X5", "获取X5版本时发生错误", e)
                0
            }
        }
    }
}
