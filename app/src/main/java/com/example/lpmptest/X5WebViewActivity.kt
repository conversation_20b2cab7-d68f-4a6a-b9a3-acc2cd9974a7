package com.example.lpmptest

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

/**
 * 腾讯X5内核WebView示例Activity
 * 
 * 注意：这个Activity需要腾讯X5 SDK才能正常工作
 * 要启用X5功能，请：
 * 1. 在 app/build.gradle.kts 中取消注释 X5 依赖
 * 2. 在 Application 中初始化 X5 内核
 * 3. 添加必要的权限和配置
 */
class X5WebViewActivity : AppCompatActivity() {
    
    private lateinit var statusTextView: TextView
    private lateinit var webViewContainer: LinearLayout
    
    companion object {
        fun start(context: Context) {
            val intent = Intent(context, X5WebViewActivity::class.java)
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        createLayout()
        checkX5Availability()
    }
    
    private fun createLayout() {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }
        
        // 标题
        val titleText = TextView(this).apply {
            text = "腾讯X5内核WebView测试"
            textSize = 18f
            setPadding(0, 0, 0, 16)
        }
        layout.addView(titleText)
        
        // 状态显示
        statusTextView = TextView(this).apply {
            text = "正在检查X5内核状态..."
            textSize = 14f
            setPadding(0, 0, 0, 16)
        }
        layout.addView(statusTextView)
        
        // 按钮组
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
        }
        
        val testButton = Button(this).apply {
            text = "测试X5功能"
            setOnClickListener { testX5Features() }
        }
        buttonLayout.addView(testButton)
        
        val loadPageButton = Button(this).apply {
            text = "加载测试页面"
            setOnClickListener { loadTestPage() }
        }
        buttonLayout.addView(loadPageButton)
        
        layout.addView(buttonLayout)
        
        // WebView容器
        webViewContainer = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
        }
        layout.addView(webViewContainer)
        
        setContentView(layout)
    }
    
    private fun checkX5Availability() {
        try {
            // 尝试检查X5是否可用
            // 注意：这些代码需要X5 SDK才能编译
            
            statusTextView.text = """
                X5内核状态检查：
                
                ⚠️ X5 SDK未集成
                
                要使用X5内核，请：
                1. 取消注释 build.gradle.kts 中的 X5 依赖
                2. 添加 Application 初始化代码
                3. 重新构建项目
                
                当前使用：系统WebView (Chrome 62)
                
                X5优势：
                • 独立的WebView引擎
                • 基于较新的Chromium内核
                • 自动更新机制
                • 更好的兼容性
            """.trimIndent()
            
            Log.d("X5WebView", "X5 SDK未集成，显示说明信息")
            
        } catch (e: Exception) {
            statusTextView.text = "检查X5状态时出错: ${e.message}"
            Log.e("X5WebView", "检查X5状态失败", e)
        }
    }
    
    private fun testX5Features() {
        try {
            // 这里应该是X5功能测试代码
            // 由于X5 SDK未集成，显示说明信息
            
            val message = """
                X5功能测试说明：
                
                如果集成了X5 SDK，这里可以测试：
                
                1. X5内核版本检查
                   QbSdk.getTbsVersion(context)
                
                2. X5可用性检查
                   QbSdk.canLoadX5(context)
                
                3. X5内核下载状态
                   QbSdk.getQbSdkVersion()
                
                4. X5 WebView创建
                   com.tencent.smtt.sdk.WebView(context)
                
                5. X5特有功能
                   - 视频全屏播放
                   - 文件下载
                   - 更好的JavaScript支持
                
                集成步骤：
                1. 添加依赖：implementation("com.tencent.tbs:tbssdk:44286")
                2. 初始化：QbSdk.initX5Environment(context, callback)
                3. 使用：com.tencent.smtt.sdk.WebView
            """.trimIndent()
            
            android.app.AlertDialog.Builder(this)
                .setTitle("X5功能测试")
                .setMessage(message)
                .setPositiveButton("我知道了", null)
                .setNeutralButton("查看集成指南") { _, _ ->
                    showIntegrationGuide()
                }
                .show()
                
        } catch (e: Exception) {
            Toast.makeText(this, "测试X5功能失败: ${e.message}", Toast.LENGTH_LONG).show()
            Log.e("X5WebView", "测试X5功能失败", e)
        }
    }
    
    private fun loadTestPage() {
        try {
            // 由于X5未集成，使用系统WebView作为演示
            val systemWebView = android.webkit.WebView(this).apply {
                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                }
                
                webViewClient = object : android.webkit.WebViewClient() {
                    override fun onPageFinished(view: android.webkit.WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        Log.d("X5WebView", "页面加载完成: $url")
                        
                        // 显示当前使用的WebView信息
                        val userAgent = view?.settings?.userAgentString ?: ""
                        val chromeVersion = Regex("Chrome/([\\d.]+)").find(userAgent)?.groupValues?.get(1) ?: "未知"
                        
                        Toast.makeText(
                            this@X5WebViewActivity, 
                            "当前使用系统WebView\nChrome版本: $chromeVersion", 
                            Toast.LENGTH_LONG
                        ).show()
                    }
                }
                
                loadUrl("https://www.baidu.com")
            }
            
            webViewContainer.removeAllViews()
            webViewContainer.addView(systemWebView)
            
            statusTextView.text = "已加载测试页面（使用系统WebView）\n如需使用X5内核，请按照说明集成X5 SDK"
            
        } catch (e: Exception) {
            Toast.makeText(this, "加载测试页面失败: ${e.message}", Toast.LENGTH_LONG).show()
            Log.e("X5WebView", "加载测试页面失败", e)
        }
    }
    
    private fun showIntegrationGuide() {
        val guide = """
            腾讯X5内核集成指南：
            
            1. 添加依赖
            在 app/build.gradle.kts 中：
            implementation("com.tencent.tbs:tbssdk:44286")
            
            2. 创建Application类
            class MyApplication : Application() {
                override fun onCreate() {
                    super.onCreate()
                    QbSdk.initX5Environment(this, object : TbsListener {
                        override fun onDownloadFinish(i: Int) {
                            Log.d("X5", "下载完成: ${'$'}i")
                        }
                        override fun onInstallFinish(i: Int) {
                            Log.d("X5", "安装完成: ${'$'}i")
                        }
                        override fun onDownloadProgress(i: Int) {
                            Log.d("X5", "下载进度: ${'$'}i")
                        }
                    })
                }
            }
            
            3. 在AndroidManifest.xml中注册Application
            <application android:name=".MyApplication" ...>
            
            4. 添加权限
            <uses-permission android:name="android.permission.INTERNET" />
            <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
            <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
            
            5. 使用X5 WebView
            import com.tencent.smtt.sdk.WebView
            val x5WebView = WebView(context)
            
            完成后重新构建项目即可使用X5内核！
        """.trimIndent()
        
        android.app.AlertDialog.Builder(this)
            .setTitle("X5集成指南")
            .setMessage(guide)
            .setPositiveButton("确定", null)
            .show()
    }
}
