package com.example.lpmptest

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.tencent.smtt.sdk.QbSdk
import com.tencent.smtt.sdk.WebView
import com.tencent.smtt.sdk.WebViewClient
import com.tencent.smtt.sdk.WebChromeClient

/**
 * X5 WebView Activity
 * 使用腾讯X5内核的WebView来替代系统WebView
 */
class X5WebViewActivity : AppCompatActivity() {

    private lateinit var x5WebView: WebView
    private lateinit var container: FrameLayout
    private var isX5Available = false

    companion object {
        private const val EXTRA_URL = "extra_url"
        private const val DEFAULT_URL = "file:///android_asset/x5_test.html"

        fun start(context: Context, url: String = DEFAULT_URL) {
            val intent = Intent(context, X5WebViewActivity::class.java).apply {
                putExtra(EXTRA_URL, url)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 创建布局
        createLayout()

        // 获取要加载的URL
        val url = intent.getStringExtra(EXTRA_URL) ?: DEFAULT_URL

        // 检查X5内核是否可用
        checkX5Availability()

        // 初始化WebView
        if (isX5Available) {
            initX5WebView(url)
        } else {
            showX5UnavailableMessage()
        }
    }

    private fun createLayout() {
        // 创建主容器
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
            )
        }

        // 创建WebView容器
        container = FrameLayout(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
            )
        }

        mainLayout.addView(container)
        setContentView(mainLayout)
    }

    private fun checkX5Availability() {
        isX5Available = QbSdk.canLoadX5(this)
        val x5Version = QbSdk.getTbsVersion(this)

        Log.d("X5WebView", "X5内核可用性: $isX5Available")
        Log.d("X5WebView", "X5内核版本: $x5Version")

        if (isX5Available) {
            Toast.makeText(this, "使用X5内核 (版本: $x5Version)", Toast.LENGTH_LONG).show()
        } else {
            Toast.makeText(this, "X5内核不可用，请检查初始化状态", Toast.LENGTH_LONG).show()
        }
    }

    private fun initX5WebView(url: String) {
        try {
            // 创建X5 WebView
            x5WebView = WebView(this).apply {
                layoutParams = FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                )
            }

            // 配置X5 WebView设置
            configureX5WebViewSettings()

            // 设置WebViewClient
            x5WebView.webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                    Log.d("X5WebView", "正在加载URL: $url")
                    view?.loadUrl(url)
                    return true
                }

                override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                    super.onPageStarted(view, url, favicon)
                    Log.d("X5WebView", "页面开始加载: $url")
                    Toast.makeText(this@X5WebViewActivity, "开始加载页面...", Toast.LENGTH_SHORT).show()
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    Log.d("X5WebView", "页面加载完成: $url")

                    // 检查X5内核版本并注入到页面
                    val x5Version = QbSdk.getTbsVersion(this@X5WebViewActivity)
                    view?.evaluateJavascript("""
                        window.X5_WEBVIEW_VERSION = $x5Version;
                        window.IS_X5_WEBVIEW = true;
                        console.log('X5 WebView 加载完成，版本:', $x5Version);
                    """, null)

                    Toast.makeText(this@X5WebViewActivity, "页面加载完成 (X5内核)", Toast.LENGTH_SHORT).show()
                }

                override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                    super.onReceivedError(view, errorCode, description, failingUrl)
                    Log.e("X5WebView", "页面加载错误 - 错误码: $errorCode, 描述: $description, URL: $failingUrl")
                    Toast.makeText(this@X5WebViewActivity, "加载失败: $description", Toast.LENGTH_LONG).show()
                }
            }

            // 设置WebChromeClient
            x5WebView.webChromeClient = object : WebChromeClient() {
                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)
                    Log.d("X5WebView", "加载进度: $newProgress%")
                }

                override fun onReceivedTitle(view: WebView?, title: String?) {
                    super.onReceivedTitle(view, title)
                    Log.d("X5WebView", "页面标题: $title")
                    <EMAIL> = "X5 WebView - $title"
                }
            }

            // 添加JavaScript接口
            x5WebView.addJavascriptInterface(X5JavaScriptInterface(), "X5Android")

            // 将WebView添加到容器
            container.addView(x5WebView)

            // 加载URL
            Log.d("X5WebView", "开始加载URL: $url")
            x5WebView.loadUrl(url)

        } catch (e: Exception) {
            Log.e("X5WebView", "初始化X5 WebView失败", e)
            Toast.makeText(this, "初始化X5 WebView失败: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    private fun configureX5WebViewSettings() {
        x5WebView.settings.apply {
            // 基本设置
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
            loadsImagesAutomatically = true

            // X5特有设置
            setAppCacheEnabled(true)
            setGeolocationEnabled(true)
            setPluginState(com.tencent.smtt.sdk.WebSettings.PluginState.ON)
            setMixedContentMode(com.tencent.smtt.sdk.WebSettings.LOAD_NORMAL)

            // 缓存设置
            cacheMode = com.tencent.smtt.sdk.WebSettings.LOAD_DEFAULT

            // 视图设置
            useWideViewPort = true
            loadWithOverviewMode = true
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false

            // 安全设置 - X5内核可能不支持这些设置，注释掉
            // allowUniversalAccessFromFileURLs = false
            // allowFileAccessFromFileURLs = false
        }
    }

    private fun showX5UnavailableMessage() {
        Toast.makeText(this, "X5内核不可用，请等待初始化完成后重试", Toast.LENGTH_LONG).show()

        // 显示详细信息对话框
        android.app.AlertDialog.Builder(this)
            .setTitle("X5内核不可用")
            .setMessage("""
                X5内核当前不可用，可能的原因：

                1. X5内核正在下载中
                2. X5内核初始化未完成
                3. 设备不支持X5内核
                4. 网络连接问题

                建议：
                • 等待几分钟后重试
                • 检查网络连接
                • 重启应用
            """.trimIndent())
            .setPositiveButton("重试") { _, _ ->
                recreate()
            }
            .setNegativeButton("返回") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && ::x5WebView.isInitialized && x5WebView.canGoBack()) {
            x5WebView.goBack()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::x5WebView.isInitialized) {
            container.removeView(x5WebView)
            x5WebView.destroy()
        }
    }

    /**
     * X5 WebView的JavaScript接口
     */
    inner class X5JavaScriptInterface {

        @android.webkit.JavascriptInterface
        fun getX5Version(): Int {
            return QbSdk.getTbsVersion(this@X5WebViewActivity)
        }

        @android.webkit.JavascriptInterface
        fun isX5Available(): Boolean {
            return QbSdk.canLoadX5(this@X5WebViewActivity)
        }

        @android.webkit.JavascriptInterface
        fun showToast(message: String) {
            runOnUiThread {
                Toast.makeText(this@X5WebViewActivity, "X5: $message", Toast.LENGTH_SHORT).show()
            }
        }

        @android.webkit.JavascriptInterface
        fun logMessage(message: String) {
            Log.d("X5JavaScript", message)
        }
    }
}
