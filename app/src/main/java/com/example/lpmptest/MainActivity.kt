package com.example.lpmptest

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.webkit.WebViewCompat
import androidx.webkit.WebSettingsCompat
import androidx.webkit.WebViewFeature
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import android.widget.Toast
import com.example.lpmptest.ui.theme.LpmpTestTheme
import com.sunmi.peripheral.printer.InnerPrinterCallback
import com.sunmi.peripheral.printer.InnerPrinterManager
import com.sunmi.peripheral.printer.SunmiPrinterService
import android.media.MediaPlayer
import com.example.lpmptest.R
import android.hardware.Camera
import android.view.View
import android.widget.FrameLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class MainActivity : ComponentActivity() {

    private lateinit var webView: WebView
    private var sunmiPrinter: SunmiPrinterService? = null
    private var camera: Camera? = null
    private var cameraPreview: CameraPreview? = null
    private var isScanning = false
    // private lateinit var webViewDebugger: WebViewDebugger

    companion object {
        private const val CAMERA_PERMISSION_REQUEST_CODE = 100
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 初始化网络安全配置
        android.webkit.WebView.setWebContentsDebuggingEnabled(true) // 启用Chrome远程调试

        webView = findViewById<WebView>(R.id.webview).apply {
            // 使用AndroidX WebKit增强功能
            initializeWebViewWithWebKit()

            settings.apply {
                javaScriptEnabled = true
                domStorageEnabled = true  // 启用DOM存储
                allowFileAccess = true    // 允许访问文件
                loadsImagesAutomatically = true  // 自动加载图片
                mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW  // 允许混合内容
                useWideViewPort = true    // 使用宽视图
                loadWithOverviewMode = true  // 加载概览模式
                javaScriptCanOpenWindowsAutomatically = true  // 允许JS打开窗口
            }
            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    Log.d("WebView", "页面加载完成: $url")
                }

                override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                    super.onReceivedError(view, errorCode, description, failingUrl)
                    Log.e("WebView", "页面加载错误 - 错误码: $errorCode, 描述: $description, URL: $failingUrl")
                    Toast.makeText(this@MainActivity, "加载页面失败: $description", Toast.LENGTH_LONG).show()
                }

                @android.annotation.TargetApi(android.os.Build.VERSION_CODES.M)
                override fun onReceivedError(view: WebView?, request: android.webkit.WebResourceRequest?, error: android.webkit.WebResourceError?) {
                    error?.let {
                        Log.e("WebView", "页面加载错误 - 错误码: ${it.errorCode}, 描述: ${it.description}, URL: ${request?.url}")
                        if (request?.isForMainFrame == true) {
                            Toast.makeText(this@MainActivity, "加载页面失败: ${it.description}", Toast.LENGTH_LONG).show()
                        }
                    }
                    super.onReceivedError(view, request, error)
                }

                override fun onReceivedSslError(view: WebView?, handler: android.webkit.SslErrorHandler?, error: android.net.http.SslError?) {
                    Log.e("WebView", "SSL错误: ${error?.toString()}")
                    // SSL证书问题通常是自签名证书或证书不受信任导致的
                    // 在开发环境中可以选择接受所有证书，但在生产环境中不推荐
                    val message = when (error?.primaryError) {
                        android.net.http.SslError.SSL_UNTRUSTED -> "证书颁发机构不受信任"
                        android.net.http.SslError.SSL_EXPIRED -> "证书已过期"
                        android.net.http.SslError.SSL_IDMISMATCH -> "证书主机名不匹配"
                        android.net.http.SslError.SSL_NOTYETVALID -> "证书尚未生效"
                        android.net.http.SslError.SSL_DATE_INVALID -> "证书日期无效"
                        else -> "未知SSL错误"
                    }

                    // 显示SSL错误对话框，让用户选择是否继续
                    android.app.AlertDialog.Builder(this@MainActivity)
                        .setTitle("SSL证书错误")
                        .setMessage("$message\n\n您想继续访问该网站吗？\n错误详情: ${error?.toString()}")
                        .setPositiveButton("继续") { _, _ ->
                            handler?.proceed()
                            Log.d("WebView", "用户选择继续访问网站，忽略SSL错误")
                        }
                        .setNegativeButton("取消") { _, _ ->
                            handler?.cancel()
                            Log.d("WebView", "用户选择取消访问网站")
                        }
                        .create()
                        .show()

                    // 不调用super以便自己处理错误
                    // super.onReceivedSslError(view, handler, error)
                }
            }
            addJavascriptInterface(WebAppInterface(this@MainActivity), "Android")
            // 加载本地 HTML 文件或者远程网页
            // 默认加载AndroidX WebKit测试页面
            loadUrl("file:///android_asset/webkit_test.html")
            // loadUrl("file:///android_asset/index.html")
            // loadUrl("https://baidu.com")
            // loadUrl("https://wms.wx-test.sheincorp.cn/#/auth-renew")
            // loadUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")
        }

        // 初始化WebView调试工具
        // webViewDebugger = WebViewDebugger(webView)

        // 设置调试按钮的点击事件（如果存在的话）
        // 注意：这些按钮在当前布局中不存在，可以通过JavaScript接口调用
        Log.d("MainActivity", "调试功能可通过JavaScript接口调用：Android.runDebugTools()")
        Log.d("MainActivity", "WebKit功能检查可通过JavaScript接口调用：Android.showWebKitFeatures()")
        Log.d("MainActivity", "WebKit测试Activity可通过代码启动：WebKitTestActivity.start(context)")

        Toast.makeText(this, "欢迎来到主页面！", Toast.LENGTH_SHORT).show()
        // 初始化打印机
        initPrinter()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1 && resultCode == Activity.RESULT_OK && data != null) {
            val bundle = data.extras
            val result = bundle?.getSerializable("data") as? ArrayList<HashMap<String, String>>
            result?.forEach {
                val type = it["TYPE"]
                val value = it["VALUE"]
                Log.i("sunmi", "Type: $type, Value: $value")
                webView.evaluateJavascript("javascript:onScanResult('$value')", null)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseCamera()
        InnerPrinterManager.getInstance().unBindService(this, null)
    }

    private fun checkCameraPermission() {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, arrayOf(android.Manifest.permission.CAMERA), CAMERA_PERMISSION_REQUEST_CODE)
        } else {
            initCamera()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            CAMERA_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    initCamera()
                } else {
                    Toast.makeText(this, "需要相机权限才能使用扫码功能", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun initCamera() {
        try {
            camera = Camera.open()
            camera?.setDisplayOrientation(90)
            cameraPreview = CameraPreview(this).apply {
                setCamera(camera)
            }
        } catch (e: Exception) {
            Log.e("Camera", "Error initializing camera", e)
            Toast.makeText(this, "无法初始化相机", Toast.LENGTH_SHORT).show()
        }
    }

    private fun releaseCamera() {
        try {
            camera?.stopPreview()
            camera?.release()
            camera = null
            cameraPreview = null
            isScanning = false
        } catch (e: Exception) {
            Log.e("Camera", "Error releasing camera", e)
        }
    }


    private fun hasScanner(ctx: Context): Boolean {
        val info = getPackageInfo(ctx, "com.sunmi.scanner")
        val versionName = info?.versionName ?: return false
        return compareVer(versionName, "4.4.4", true, 3)
    }

    private fun getPackageInfo(context: Context, pkg: String): PackageInfo? {
        return try {
            context.packageManager.getPackageInfo(pkg, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
            null
        }
    }


    private fun compareVer(nVer: String, oVer: String, isEq: Boolean, bit: Int): Boolean {
        if (nVer.isEmpty() || oVer.isEmpty()) return false
        val nArr = nVer.split(".")
        val oArr = oVer.split(".")
        if (nArr.size < bit || oArr.size < bit) return false
        var vup = false
        for (i in 0 until bit) {
            val n = nArr[i].toInt()
            val o = oArr[i].toInt()
            if (n >= o) {
                if (n > o) {
                    vup = true
                    break
                } else if (isEq && i == (bit - 1)) {
                    vup = true
                    break
                }
            } else {
                break
            }
        }
        return vup
    }

    private fun initPrinter() {
        val printerCallback = object : InnerPrinterCallback() {
            // 当 Sunmi 打印机连接成功时的回调
            override fun onConnected(service: SunmiPrinterService) {
                sunmiPrinter = service
                Log.d("Printer", "Sunmi Printer Connected!")
            }

            // 当 Sunmi 打印机断开连接时的回调（提供默认行为防止崩溃）
            override fun onDisconnected() {
                sunmiPrinter = null
                Log.d("Printer", "Sunmi Printer Disconnected!")
            }
        }

        // 绑定 Sunmi 打印服务
        try {
            InnerPrinterManager.getInstance().bindService(this, printerCallback)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("Printer", "Failed to bind Sunmi Printer Service", e)
        }
    }


    /**
     * 使用AndroidX WebKit增强WebView功能
     */
    private fun WebView.initializeWebViewWithWebKit() {
        try {
            // 检查并启用AndroidX WebKit功能
            Log.d("WebKit", "初始化AndroidX WebKit功能...")

            // 检查是否支持强制暗色模式
            if (WebViewFeature.isFeatureSupported(WebViewFeature.ALGORITHMIC_DARKENING)) {
                WebSettingsCompat.setAlgorithmicDarkeningAllowed(this.settings, true)
                Log.d("WebKit", "已启用算法暗色模式支持")
            }

            // 检查是否支持安全浏览
            if (WebViewFeature.isFeatureSupported(WebViewFeature.SAFE_BROWSING_ENABLE)) {
                WebSettingsCompat.setSafeBrowsingEnabled(this.settings, true)
                Log.d("WebKit", "已启用安全浏览")
            }

            // 检查是否支持离线页面
            if (WebViewFeature.isFeatureSupported(WebViewFeature.OFF_SCREEN_PRERASTER)) {
                WebSettingsCompat.setOffscreenPreRaster(this.settings, true)
                Log.d("WebKit", "已启用离线预渲染")
            }

            // 检查是否支持禁用网络图片
            if (WebViewFeature.isFeatureSupported(WebViewFeature.DISABLED_ACTION_MODE_MENU_ITEMS)) {
                // 可以根据需要配置
                Log.d("WebKit", "支持禁用操作菜单项")
            }

            // 获取WebView版本信息
            val webViewPackageInfo = WebViewCompat.getCurrentWebViewPackage(this@MainActivity)
            if (webViewPackageInfo != null) {
                Log.d("WebKit", "WebView版本: ${webViewPackageInfo.versionName}")
                Log.d("WebKit", "WebView包名: ${webViewPackageInfo.packageName}")
            }

            // 检查是否支持多进程
            if (WebViewFeature.isFeatureSupported(WebViewFeature.MULTI_PROCESS)) {
                val isMultiProcess = WebViewCompat.isMultiProcessEnabled()
                Log.d("WebKit", "多进程模式: $isMultiProcess")
            }

        } catch (e: Exception) {
            Log.e("WebKit", "初始化AndroidX WebKit功能时出错", e)
        }
    }

    private fun getSN(): String {
        var serial: String? = null
        try {
            val c = Class.forName("android.os.SystemProperties")
            val get = c.getMethod("get", String::class.java)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                serial = get.invoke(c, "ro.sunmi.serial") as String?
            } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                try {
                    @Suppress("DEPRECATION")
                    serial = android.os.Build.getSerial()
                } catch (e: SecurityException) {
                    Log.w("MainActivity", "无法获取设备序列号，权限不足")
                    serial = "权限不足"
                }
            } else {
                serial = get.invoke(c, "ro.serialno") as String?
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return serial ?: "未知"
    }

    inner class WebAppInterface(private val activity: Activity) {
        // 使用MediaPlayer播放支付成功音效
        private var mediaPlayer: MediaPlayer? = null

        @JavascriptInterface
        fun playPaySound() {
            activity.runOnUiThread {
                try {
                    // 释放之前的MediaPlayer资源
                    mediaPlayer?.release()

                    // 创建新的MediaPlayer并播放支付成功音效
                    mediaPlayer = MediaPlayer.create(activity, R.raw.payment_success)
                    mediaPlayer?.setOnCompletionListener { mp ->
                        mp.release()
                        mediaPlayer = null
                    }
                    mediaPlayer?.start()

                    Toast.makeText(activity, "播放支付成功音效", Toast.LENGTH_SHORT).show()
                } catch (e: Exception) {
                    e.printStackTrace()
                    Toast.makeText(activity, "播放音效失败", Toast.LENGTH_SHORT).show()
                }
            }
        }

        @JavascriptInterface
        fun previewTone() {
            playPaySound()
        }

        @JavascriptInterface
        fun startScan() {
            activity.runOnUiThread {
                if (!isScanning) {
                    checkCameraPermission()
                    val container = activity.findViewById<FrameLayout>(R.id.camera_preview_container)
                    container.visibility = View.VISIBLE
                    cameraPreview?.let { preview ->
                        container.addView(preview)
                    }
                    isScanning = true
                    Toast.makeText(activity, "相机预览已启动", Toast.LENGTH_SHORT).show()
                } else {
                    releaseCamera()
                    val container = activity.findViewById<FrameLayout>(R.id.camera_preview_container)
                    container.visibility = View.GONE
                    container.removeAllViews()
                    Toast.makeText(activity, "相机预览已关闭", Toast.LENGTH_SHORT).show()
                }
            }
        }
        @JavascriptInterface
        fun getSN(): String {
            return <EMAIL>()
        }

        @JavascriptInterface
        fun print() {
            activity.runOnUiThread {
                Toast.makeText(activity, "正在启动打印功能...", Toast.LENGTH_SHORT).show()
            }
            runOnUiThread {
                try {
                    sunmiPrinter?.apply {
                        setAlignment(1, null) // 居中对齐
                        printText("=== 测试打印 ===\n", null)
                        printText("这是一个测试内容\n", null)
                        printText("-------------\n", null)
                        lineWrap(3, null) // 走纸3行
                    }
                } catch (e: Exception) {
                    Toast.makeText(activity, "打印失败", Toast.LENGTH_SHORT).show()
                    e.printStackTrace()
                }
            }
        }

        @JavascriptInterface
        fun loadUrl(url: String) {
            activity.runOnUiThread {
                try {
                    Toast.makeText(activity, "正在加载: $url", Toast.LENGTH_SHORT).show()
                    webView.loadUrl(url)
                } catch (e: Exception) {
                    Toast.makeText(activity, "加载URL失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "加载URL失败", e)
                }
            }
        }

        @JavascriptInterface
        fun debugWebView() {
            activity.runOnUiThread {
                val dialog = android.app.AlertDialog.Builder(activity)
                    .setTitle("选择要加载的URL")
                    .setItems(arrayOf(
                        "百度",
                        "SheinCorp",
                        "DotFashion",
                        "查看WebView设置",
                        "网络连接测试"
                    )) { _, which ->
                        when (which) {
                            0 -> webView.loadUrl("https://baidu.com")
                            1 -> webView.loadUrl("https://wms.wx-test.sheincorp.cn/#/auth-renew")
                            2 -> webView.loadUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")
                            3 -> showWebViewSettings()
                            4 -> testNetworkConnection()
                        }
                    }
                    .setNegativeButton("取消", null)
                    .create()
                dialog.show()
            }
        }

        @JavascriptInterface
        fun showWebViewSettings() {
            activity.runOnUiThread {
                val settings = webView.settings

                // 获取WebView版本信息
                val webViewPackageInfo = WebViewCompat.getCurrentWebViewPackage(activity)
                val webViewVersion = webViewPackageInfo?.versionName ?: "未知"
                val webViewPackage = webViewPackageInfo?.packageName ?: "未知"

                // 检查Chrome版本
                val userAgent = settings.userAgentString
                val chromeVersionRegex = Regex("Chrome/([\\d.]+)")
                val chromeVersion = chromeVersionRegex.find(userAgent)?.groupValues?.get(1) ?: "未知"

                val message = """
                    WebView详细信息:

                    === 版本信息 ===
                    - WebView版本: $webViewVersion
                    - WebView包名: $webViewPackage
                    - Chrome内核版本: $chromeVersion
                    - Android版本: ${android.os.Build.VERSION.RELEASE}
                    - API级别: ${android.os.Build.VERSION.SDK_INT}

                    === 设置信息 ===
                    - JavaScript启用: ${settings.javaScriptEnabled}
                    - DOM存储启用: ${settings.domStorageEnabled}
                    - 文件访问: ${settings.allowFileAccess}
                    - 自动加载图片: ${settings.loadsImagesAutomatically}
                    - 混合内容模式: ${settings.mixedContentMode}

                    === UserAgent ===
                    ${settings.userAgentString}

                    === 版本说明 ===
                    Chrome 62 (2017年) 确实较老，建议：
                    1. 更新设备WebView组件
                    2. 使用现代Web标准的兼容写法
                    3. 考虑使用第三方WebView引擎
                """.trimIndent()

                Toast.makeText(activity, "正在查看WebView详细信息", Toast.LENGTH_SHORT).show()
                Log.d("WebViewSettings", message)

                val dialog = android.app.AlertDialog.Builder(activity)
                    .setTitle("WebView详细信息")
                    .setMessage(message)
                    .setPositiveButton("确定", null)
                    .setNeutralButton("复制信息") { _, _ ->
                        val clipboard = activity.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                        val clip = android.content.ClipData.newPlainText("WebView Info", message)
                        clipboard.setPrimaryClip(clip)
                        Toast.makeText(activity, "信息已复制到剪贴板", Toast.LENGTH_SHORT).show()
                    }
                    .create()
                dialog.show()
            }
        }

        @JavascriptInterface
        fun logConsole(level: String, message: String) {
            when (level) {
                "ERROR" -> Log.e("WebConsole", message)
                "WARN" -> Log.w("WebConsole", message)
                "INFO" -> Log.i("WebConsole", message)
                else -> Log.d("WebConsole", message)
            }
        }

        @JavascriptInterface
        fun logNetwork(type: String, message: String) {
            Log.d("WebNetwork", "$type: $message")
        }

        @JavascriptInterface
        fun logPageEvent(event: String, data: String) {
            Log.d("WebPageEvent", "$event: $data")
        }

        @JavascriptInterface
        fun testLoadDotfashion() {
            activity.runOnUiThread {
                try {
                    Toast.makeText(activity, "正在测试加载dotfashion...", Toast.LENGTH_SHORT).show()

                    // 1. 先注入调试脚本
                    // webViewDebugger.injectDebugScript()

                    // 2. 加载网页
                    webView.loadUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")

                    // 3. 延迟检查加载状态
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        // webViewDebugger.checkLoadingErrors()
                        // webViewDebugger.getDOMStructure()
                        // webViewDebugger.checkNetworkStatus()
                        Log.d("WebView", "页面加载完成，检查状态")
                    }, 5000)
                } catch (e: Exception) {
                    Toast.makeText(activity, "测试加载失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "测试加载失败", e)
                }
            }
        }

        @JavascriptInterface
        fun runDebugTools() {
            activity.runOnUiThread {
                try {
                    val dialog = android.app.AlertDialog.Builder(activity)
                        .setTitle("WebView调试工具")
                        .setItems(arrayOf(
                            "检查页面错误",
                            "查看DOM结构",
                            "查看网络状态",
                            "注入调试脚本",
                            "清除缓存并重新加载",
                            "测试dotfashion加载",
                            "网络连接测试",
                            "WebView设置优化",
                            "AndroidX WebKit功能检查"
                        )) { _, which ->
                            when (which) {
                                0 -> Log.d("WebView", "检查页面错误")
                                1 -> Log.d("WebView", "查看DOM结构")
                                2 -> Log.d("WebView", "查看网络状态")
                                3 -> Log.d("WebView", "注入调试脚本")
                                4 -> {
                                    webView.clearCache(true)
                                    webView.clearHistory()
                                    webView.reload()
                                    Toast.makeText(activity, "已清除缓存并重新加载", Toast.LENGTH_SHORT).show()
                                }
                                5 -> testLoadDotfashion()
                                6 -> testNetworkConnection()
                                7 -> optimizeWebViewSettings()
                                8 -> showWebKitFeatures()
                            }
                        }
                        .setNegativeButton("取消", null)
                        .create()
                    dialog.show()
                } catch (e: Exception) {
                    Toast.makeText(activity, "调试工具错误: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "调试工具错误", e)
                }
            }
        }

        @JavascriptInterface
        fun optimizeWebViewSettings() {
            activity.runOnUiThread {
                try {
                    // 优化WebView设置
                    webView.settings.apply {
                        // 启用更多功能
                        javaScriptEnabled = true
                        domStorageEnabled = true
                        databaseEnabled = true
                        // setAppCacheEnabled(true) // 已弃用
                        allowFileAccess = true
                        allowContentAccess = true
                        loadsImagesAutomatically = true
                        blockNetworkImage = false
                        mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW

                        // 设置缓存模式
                        cacheMode = android.webkit.WebSettings.LOAD_DEFAULT

                        // 设置UA
                        val originalUA = userAgentString
                        userAgentString = "$originalUA WebViewDebug"
                    }

                    // 显示设置已优化的提示
                    val message = """
                        WebView设置已优化:
                        - JavaScript: 已启用
                        - DOM存储: 已启用
                        - 数据库: 已启用
                        - 应用缓存: 已启用
                        - 文件访问: 已启用
                        - 内容访问: 已启用
                        - 混合内容: 始终允许
                        - 缓存模式: 默认
                        - UA: 已添加标识

                        请重新加载页面以应用新设置。
                    """.trimIndent()

                    android.app.AlertDialog.Builder(activity)
                        .setTitle("WebView设置已优化")
                        .setMessage(message)
                        .setPositiveButton("重新加载") { _, _ ->
                            webView.reload()
                        }
                        .setNegativeButton("确定", null)
                        .create()
                        .show()

                } catch (e: Exception) {
                    Toast.makeText(activity, "优化WebView设置失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "优化WebView设置失败", e)
                }
            }
        }

        @JavascriptInterface
        fun showWebKitFeatures() {
            activity.runOnUiThread {
                try {
                    val features = mutableListOf<String>()

                    // 检查Chrome版本
                    val userAgent = webView.settings.userAgentString
                    val chromeVersionRegex = Regex("Chrome/([\\d.]+)")
                    val chromeVersion = chromeVersionRegex.find(userAgent)?.groupValues?.get(1) ?: "未知"
                    val chromeVersionNumber = chromeVersion.split(".")[0].toIntOrNull() ?: 0

                    // 检查各种AndroidX WebKit功能
                    val featuresToCheck = mapOf(
                        "算法暗色模式" to WebViewFeature.ALGORITHMIC_DARKENING,
                        "安全浏览" to WebViewFeature.SAFE_BROWSING_ENABLE,
                        "离线预渲染" to WebViewFeature.OFF_SCREEN_PRERASTER,
                        "多进程模式" to WebViewFeature.MULTI_PROCESS,
                        "禁用操作菜单" to WebViewFeature.DISABLED_ACTION_MODE_MENU_ITEMS,
                        "WebMessage监听器" to WebViewFeature.WEB_MESSAGE_LISTENER,
                        "文档开始JavaScript" to WebViewFeature.DOCUMENT_START_SCRIPT,
                        "代理覆盖" to WebViewFeature.PROXY_OVERRIDE,
                        "强制暗色模式" to WebViewFeature.FORCE_DARK,
                        "强制暗色策略" to WebViewFeature.FORCE_DARK_STRATEGY
                    )

                    features.add("=== AndroidX WebKit功能支持情况 ===\n")

                    // 获取WebView版本信息
                    val webViewPackageInfo = WebViewCompat.getCurrentWebViewPackage(activity)
                    if (webViewPackageInfo != null) {
                        features.add("WebView版本: ${webViewPackageInfo.versionName}")
                        features.add("WebView包名: ${webViewPackageInfo.packageName}")
                    }
                    features.add("Chrome内核版本: $chromeVersion")

                    // 版本评估
                    when {
                        chromeVersionNumber >= 90 -> features.add("✅ Chrome版本: 优秀 (90+)")
                        chromeVersionNumber >= 70 -> features.add("✅ Chrome版本: 良好 (70+)")
                        chromeVersionNumber >= 60 -> features.add("⚠️ Chrome版本: 较老 (60+) - 建议更新")
                        else -> features.add("❌ Chrome版本: 过老 (<60) - 强烈建议更新")
                    }
                    features.add("")

                    // 检查多进程状态
                    if (WebViewFeature.isFeatureSupported(WebViewFeature.MULTI_PROCESS)) {
                        val isMultiProcess = WebViewCompat.isMultiProcessEnabled()
                        features.add("多进程模式状态: ${if (isMultiProcess) "已启用" else "未启用"}\n")
                    }

                    // 检查各个功能
                    featuresToCheck.forEach { (name, feature) ->
                        val isSupported = WebViewFeature.isFeatureSupported(feature)
                        features.add("$name: ${if (isSupported) "✓ 支持" else "✗ 不支持"}")
                    }

                    features.add("\n=== 当前WebView设置 ===")
                    features.add("JavaScript: ${webView.settings.javaScriptEnabled}")
                    features.add("DOM存储: ${webView.settings.domStorageEnabled}")
                    features.add("文件访问: ${webView.settings.allowFileAccess}")
                    features.add("混合内容模式: ${webView.settings.mixedContentMode}")

                    // 添加建议
                    if (chromeVersionNumber < 70) {
                        features.add("\n=== 升级建议 ===")
                        features.add("• 尝试更新Android System WebView")
                        features.add("• 升级Android系统版本")
                        features.add("• 考虑使用兼容模式")
                        features.add("• 避免使用最新Web API")
                    }

                    val message = features.joinToString("\n")

                    val dialog = android.app.AlertDialog.Builder(activity)
                        .setTitle("AndroidX WebKit功能")
                        .setMessage(message)
                        .setPositiveButton("确定", null)
                        .setNeutralButton("复制信息") { _, _ ->
                            val clipboard = activity.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                            val clip = android.content.ClipData.newPlainText("WebKit Features", message)
                            clipboard.setPrimaryClip(clip)
                            Toast.makeText(activity, "已复制到剪贴板", Toast.LENGTH_SHORT).show()
                        }

                    // 如果版本较老，添加升级按钮
                    if (chromeVersionNumber < 70) {
                        dialog.setNegativeButton("查看升级方案") { _, _ ->
                            showWebViewUpgradeSuggestions()
                        }
                    }

                    dialog.create().show()

                } catch (e: Exception) {
                    Toast.makeText(activity, "获取WebKit功能信息失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebKit", "获取WebKit功能信息失败", e)
                }
            }
        }

        @JavascriptInterface
        fun showWebViewUpgradeSuggestions() {
            activity.runOnUiThread {
                val suggestions = """
                    WebView升级方案：

                    1. 【推荐】更新Android System WebView
                       • 打开Google Play商店
                       • 搜索"Android System WebView"
                       • 点击更新（如果可用）

                    2. 检查系统更新
                       • 设置 → 系统更新
                       • 下载并安装可用更新

                    3. 启用兼容模式
                       • 应用会自动适配老版本
                       • 某些功能可能受限

                    4. 第三方WebView引擎
                       • 腾讯X5内核
                       • UC WebView
                       • 需要额外集成

                    当前状态：Chrome 62 (2017年)
                    建议版本：Chrome 70+ (2018年+)
                """.trimIndent()

                android.app.AlertDialog.Builder(activity)
                    .setTitle("WebView升级建议")
                    .setMessage(suggestions)
                    .setPositiveButton("我知道了", null)
                    .setNeutralButton("启用兼容模式") { _, _ ->
                        enableCompatibilityMode()
                    }
                    .create()
                    .show()
            }
        }

        @JavascriptInterface
        fun enableCompatibilityMode() {
            activity.runOnUiThread {
                try {
                    // 为老版本WebView启用特殊设置
                    webView.settings.apply {
                        // 启用所有可能的缓存
                        cacheMode = android.webkit.WebSettings.LOAD_DEFAULT

                        // 优化渲染
                        useWideViewPort = true
                        loadWithOverviewMode = true

                        // 启用缩放
                        setSupportZoom(true)
                        builtInZoomControls = true
                        displayZoomControls = false

                        // 安全设置
                        allowUniversalAccessFromFileURLs = false
                        allowFileAccessFromFileURLs = false
                    }

                    // 注入兼容性JavaScript
                    webView.evaluateJavascript("""
                        window.WEBVIEW_COMPATIBILITY_MODE = true;
                        window.CHROME_VERSION = ${getCurrentChromeVersion()};
                        console.log('兼容模式已启用，Chrome版本:', window.CHROME_VERSION);
                    """, null)

                    Toast.makeText(activity, "兼容模式已启用", Toast.LENGTH_LONG).show()
                    Log.d("WebView", "兼容模式已启用")

                } catch (e: Exception) {
                    Toast.makeText(activity, "启用兼容模式失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "启用兼容模式失败", e)
                }
            }
        }

        @JavascriptInterface
        fun getCurrentChromeVersion(): Int {
            val userAgent = webView.settings.userAgentString
            val chromeVersionRegex = Regex("Chrome/([\\d.]+)")
            val chromeVersion = chromeVersionRegex.find(userAgent)?.groupValues?.get(1) ?: "0"
            return chromeVersion.split(".")[0].toIntOrNull() ?: 0
        }

        @JavascriptInterface
        fun openX5TestActivity() {
            activity.runOnUiThread {
                try {
                    X5WebViewActivity.start(activity)
                    Toast.makeText(activity, "正在打开X5内核测试Activity", Toast.LENGTH_SHORT).show()
                } catch (e: Exception) {
                    Toast.makeText(activity, "打开X5测试Activity失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("MainActivity", "打开X5测试Activity失败", e)
                }
            }
        }

        @JavascriptInterface
        fun testNetworkConnection() {
            activity.runOnUiThread {
                val dialog = android.app.AlertDialog.Builder(activity)
                    .setTitle("网络连接测试")
                    .setItems(arrayOf(
                        "测试 baidu.com",
                        "测试 sheincorp.cn",
                        "测试 dotfashion.cn",
                        "自定义URL测试"
                    )) { _, which ->
                        when (which) {
                            0 -> testUrl("https://baidu.com")
                            1 -> testUrl("https://wms.wx-test.sheincorp.cn/#/auth-renew")
                            2 -> testUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")
                            3 -> showCustomUrlDialog()
                        }
                    }
                    .setNegativeButton("取消", null)
                    .create()
                dialog.show()
            }
        }

        private fun testUrl(url: String) {
            activity.runOnUiThread {
                Toast.makeText(activity, "正在测试URL: $url", Toast.LENGTH_SHORT).show()

                // 显示进度对话框
                val progressDialog = android.app.ProgressDialog(activity).apply {
                    setTitle("正在测试")
                    setMessage("正在测试URL连接: $url")
                    setCancelable(false)
                    show()
                }

                // 执行测试 - 简化版本
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    progressDialog.dismiss()

                    // 显示测试结果
                    android.app.AlertDialog.Builder(activity)
                        .setTitle("测试结果")
                        .setMessage("正在测试URL: $url\n\n请在WebView中查看加载结果")
                        .setPositiveButton("确定", null)
                        .setNeutralButton("在WebView中加载") { _, _ ->
                            webView.loadUrl(url)
                        }
                        .create()
                        .show()
                }, 2000)
            }
        }

        private fun showCustomUrlDialog() {
            activity.runOnUiThread {
                val input = android.widget.EditText(activity).apply {
                    hint = "请输入URL (例如 https://example.com)"
                    setText("https://")
                }

                android.app.AlertDialog.Builder(activity)
                    .setTitle("输入URL")
                    .setView(input)
                    .setPositiveButton("测试") { _, _ ->
                        val url = input.text.toString().trim()
                        if (url.isNotEmpty() && android.webkit.URLUtil.isValidUrl(url)) {
                            testUrl(url)
                        } else {
                            Toast.makeText(activity, "请输入有效的URL", Toast.LENGTH_SHORT).show()
                        }
                    }
                    .setNegativeButton("取消", null)
                    .create()
                    .show()
            }
        }
    }
}

@Composable
fun Greeting(name: String, modifier: Modifier = Modifier) {
    Text(
        text = "1661Hello23 $name!",
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    LpmpTestTheme {
        Greeting("Android")
    }
}