package com.example.lpmptest

import android.content.Context
import android.hardware.Camera
import android.util.AttributeSet
import android.util.Log
import android.view.SurfaceHolder
import android.view.SurfaceView

class CameraPreview @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : SurfaceView(context, attrs, defStyleAttr), SurfaceHolder.Callback {

    private var camera: Camera? = null
    private val surfaceHolder: SurfaceHolder = holder.apply {
        addCallback(this@CameraPreview)
    }

    fun setCamera(camera: Camera?) {
        this.camera = camera
        try {
            camera?.setPreviewDisplay(surfaceHolder)
        } catch (e: Exception) {
            Log.e(TAG, "Error setting camera preview", e)
        }
    }

    override fun surfaceCreated(holder: SurfaceHolder) {
        try {
            camera?.setPreviewDisplay(holder)
            camera?.startPreview()
        } catch (e: Exception) {
            Log.e(TAG, "Error starting camera preview", e)
        }
    }

    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        if (surfaceHolder.surface == null) return

        try {
            camera?.stopPreview()
        } catch (e: Exception) {
            // 忽略尝试停止不存在的预览时的异常
        }

        try {
            camera?.setPreviewDisplay(holder)
            camera?.startPreview()
        } catch (e: Exception) {
            Log.e(TAG, "Error restarting camera preview", e)
        }
    }

    override fun surfaceDestroyed(holder: SurfaceHolder) {
        // 释放相机预览
        try {
            camera?.stopPreview()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping camera preview", e)
        }
    }

    companion object {
        private const val TAG = "CameraPreview"
    }
}